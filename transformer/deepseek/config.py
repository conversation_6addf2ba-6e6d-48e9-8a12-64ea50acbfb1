# config.py（修改语言相关参数）
from pydantic import BaseModel
import torch

class ModelConfig(BaseModel):
    d_model: int = 512
    nhead: int = 8
    num_layers: int = 6
    dim_feedforward: int = 2048
    dropout: float = 0.1
    max_seq_len: int = 100
    src_vocab_size: int = 8000  # 英语词表
    tgt_vocab_size: int = 8000  # 意大利语词表
    pad_idx: int = 0

class TrainingConfig(BaseModel):
    batch_size: int = 64
    lr: float = 0.0001
    epochs: int = 20
    train_size: int = 10000    # 根据实际数据集调整
    valid_size: int = 2000
    log_interval: int = 50
    save_path: str = "en_it_transformer.pt"
    device: str = "mps" if torch.backends.mps.is_available() else "cpu"