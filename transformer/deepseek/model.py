# model.py
import math
import torch
import torch.nn as nn
from config import ModelConfig

class PositionalEncoding(nn.Module):
    def __init__(self, config: ModelConfig):
        super().__init__()
        pe = torch.zeros(config.max_seq_len, config.d_model)
        position = torch.arange(0, config.max_seq_len, dtype=torch.float).unsqueeze(1)
        div_term = torch.exp(torch.arange(0, config.d_model, 2).float() * (-math.log(10000.0) / config.d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        self.register_buffer('pe', pe.unsqueeze(0))

    def forward(self, x):
        return x + self.pe[:, :x.size(1)]

class MultiHeadAttention(nn.Module):
    def __init__(self, config: ModelConfig):
        super().__init__()
        self.d_model = config.d_model
        self.nhead = config.nhead
        self.head_dim = config.d_model // config.nhead
        
        # 手动初始化权重矩阵
        self.W_q = nn.Parameter(torch.randn(config.d_model, config.d_model))
        self.W_k = nn.Parameter(torch.randn(config.d_model, config.d_model))
        self.W_v = nn.Parameter(torch.randn(config.d_model, config.d_model))
        self.W_o = nn.Parameter(torch.randn(config.d_model, config.d_model))
        
        self.dropout = nn.Dropout(config.dropout)

    def scaled_dot_product_attention(self, Q, K, V, mask=None):
        # 手动实现注意力计算
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.head_dim)
        
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # 手动实现softmax
        max_scores = torch.max(scores, dim=-1, keepdim=True).values
        exp_scores = torch.exp(scores - max_scores)
        sum_exp = torch.sum(exp_scores, dim=-1, keepdim=True)
        attn = exp_scores / sum_exp
        
        attn = self.dropout(attn)
        output = torch.matmul(attn, V)
        return output

    def split_heads(self, x):
        batch_size = x.size(0)
        return x.view(batch_size, -1, self.nhead, self.head_dim).transpose(1, 2)

    def combine_heads(self, x):
        batch_size = x.size(0)
        return x.transpose(1, 2).contiguous().view(batch_size, -1, self.d_model)

    def forward(self, Q, K, V, mask=None):
        Q = torch.matmul(Q, self.W_q)
        K = torch.matmul(K, self.W_k)
        V = torch.matmul(V, self.W_v)
        
        Q = self.split_heads(Q)
        K = self.split_heads(K)
        V = self.split_heads(V)
        
        attn_output = self.scaled_dot_product_attention(Q, K, V, mask)
        output = self.combine_heads(attn_output)
        output = torch.matmul(output, self.W_o)
        return output

class PositionWiseFeedForward(nn.Module):
    def __init__(self, config: ModelConfig):
        super().__init__()
        self.linear1 = nn.Linear(config.d_model, config.dim_feedforward)
        self.linear2 = nn.Linear(config.dim_feedforward, config.d_model)
        self.dropout = nn.Dropout(config.dropout)

    def forward(self, x):
        return self.linear2(self.dropout(torch.relu(self.linear1(x))))

class EncoderLayer(nn.Module):
    def __init__(self, config: ModelConfig):
        super().__init__()
        self.self_attn = MultiHeadAttention(config)
        self.ffn = PositionWiseFeedForward(config)
        self.norm1 = nn.LayerNorm(config.d_model)
        self.norm2 = nn.LayerNorm(config.d_model)
        self.dropout = nn.Dropout(config.dropout)

    def forward(self, x, mask):
        attn_output = self.self_attn(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))
        ffn_output = self.ffn(x)
        x = self.norm2(x + self.dropout(ffn_output))
        return x

class DecoderLayer(nn.Module):
    def __init__(self, config: ModelConfig):
        super().__init__()
        self.self_attn = MultiHeadAttention(config)
        self.cross_attn = MultiHeadAttention(config)
        self.ffn = PositionWiseFeedForward(config)
        self.norm1 = nn.LayerNorm(config.d_model)
        self.norm2 = nn.LayerNorm(config.d_model)
        self.norm3 = nn.LayerNorm(config.d_model)
        self.dropout = nn.Dropout(config.dropout)

    def forward(self, x, enc_output, src_mask, tgt_mask):
        attn_output = self.self_attn(x, x, x, tgt_mask)
        x = self.norm1(x + self.dropout(attn_output))
        attn_output = self.cross_attn(x, enc_output, enc_output, src_mask)
        x = self.norm2(x + self.dropout(attn_output))
        ffn_output = self.ffn(x)
        x = self.norm3(x + self.dropout(ffn_output))
        return x

class Transformer(nn.Module):
    def __init__(self, config: ModelConfig):
        super().__init__()
        self.encoder_embed = nn.Embedding(config.src_vocab_size, config.d_model)
        self.decoder_embed = nn.Embedding(config.tgt_vocab_size, config.d_model)
        self.pos_encoding = PositionalEncoding(config)
        
        self.encoder_layers = nn.ModuleList([EncoderLayer(config) for _ in range(config.num_layers)])
        self.decoder_layers = nn.ModuleList([DecoderLayer(config) for _ in range(config.num_layers)])
        
        self.fc_out = nn.Linear(config.d_model, config.tgt_vocab_size)
        self.dropout = nn.Dropout(config.dropout)

    def encode(self, src, src_mask):
        src = self.encoder_embed(src)
        src = self.pos_encoding(src)
        for layer in self.encoder_layers:
            src = layer(src, src_mask)
        return src

    def decode(self, tgt, enc_output, src_mask, tgt_mask):
        tgt = self.decoder_embed(tgt)
        tgt = self.pos_encoding(tgt)
        for layer in self.decoder_layers:
            tgt = layer(tgt, enc_output, src_mask, tgt_mask)
        return self.fc_out(tgt)

    def forward(self, src, tgt, src_mask, tgt_mask):
        enc_output = self.encode(src, src_mask)
        return self.decode(tgt, enc_output, src_mask, tgt_mask)