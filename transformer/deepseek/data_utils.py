# data_utils.py（核心修改）
import os
import json
import torch
from torch.utils.data import Dataset, DataLoader
from collections import Counter

class BilingualDataset(Dataset):
    def __init__(self, src_sentences, tgt_sentences, src_vocab, tgt_vocab, max_len):
        self.src_sentences = src_sentences
        self.tgt_sentences = tgt_sentences
        self.src_vocab = src_vocab
        self.tgt_vocab = tgt_vocab
        self.max_len = max_len

    def __len__(self):
        return len(self.src_sentences)

    def __getitem__(self, idx):
        src = self.src_sentences[idx]
        tgt = self.tgt_sentences[idx]
        
        # 英语（src）和意大利语（tgt）处理
        src_ids = [self.src_vocab['<bos>']] + self.tokenize(src, 'en') + [self.src_vocab['<eos>']]
        tgt_ids = [self.tgt_vocab['<bos>']] + self.tokenize(tgt, 'it') + [self.tgt_vocab['<eos>']]
        
        src_ids = self.pad_truncate(src_ids)
        tgt_ids = self.pad_truncate(tgt_ids)
        
        return torch.tensor(src_ids), torch.tensor(tgt_ids)

    def tokenize(self, text, lang):
        # 英语和意大利语均按空格分词
        tokens = text.lower().split() if lang == 'en' else text.split()  # 意大利语保留大小写
        vocab = self.src_vocab if lang == 'en' else self.tgt_vocab
        return [vocab.get(t, vocab['<unk>']) for t in tokens]

    def pad_truncate(self, ids):
        pad_token = self.src_vocab['<pad>'] if len(ids) == self.max_len else self.tgt_vocab['<pad>']
        if len(ids) >= self.max_len:
            return ids[:self.max_len]
        return ids + [pad_token] * (self.max_len - len(ids))

def build_vocab(sentences, max_size, lang):
    counter = Counter()
    special_tokens = ['<pad>', '<unk>', '<bos>', '<eos>']
    
    for sent in sentences:
        # 意大利语保留原始大小写
        tokens = sent.lower().split() if lang == 'en' else sent.split()
        counter.update(tokens)
    
    vocab = {token: idx for idx, token in enumerate(special_tokens)}
    for token, _ in counter.most_common(max_size - len(special_tokens)):
        if token not in vocab:
            vocab[token] = len(vocab)
    return vocab

def load_data():
    from datasets import load_dataset
    dataset = load_dataset("Helsinki-NLP/opus_books", "en-it")
    
    # 提取英意句子
    en_sents = [ex['translation']['en'] for ex in dataset['train']]
    it_sents = [ex['translation']['it'] for ex in dataset['train']]
    
    # 构建词表
    src_vocab = build_vocab(en_sents[:12000], 8000, 'en')
    tgt_vocab = build_vocab(it_sents[:12000], 8000, 'it')
    
    # 保存词表
    with open('en_vocab.json', 'w') as f:
        json.dump(src_vocab, f, ensure_ascii=False)
    with open('it_vocab.json', 'w') as f:
        json.dump(tgt_vocab, f, ensure_ascii=False)
    
    return BilingualDataset(en_sents[:10000], it_sents[:10000], src_vocab, tgt_vocab, 100), \
           BilingualDataset(en_sents[10000:12000], it_sents[10000:12000], src_vocab, tgt_vocab, 100)